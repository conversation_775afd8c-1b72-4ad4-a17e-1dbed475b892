#!/usr/bin/env python3
"""
青城住房监控系统 - 一键启动程序
统一管理监控服务和Web控制台的启动、停止和监控
"""

import os
import sys
import time
import signal
import subprocess
import threading
import requests
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.unified_config import get_config
from core.utils.unified_logging import setup_unified_logging, log_with_context


class ServiceManager:
    """服务管理器 - 统一管理多个服务进程"""

    def __init__(self):
        self.services = {}
        self.shutdown_requested = False
        self.logger = setup_unified_logging("服务管理器")

        # 项目根目录
        self.project_root = Path(__file__).parent

        # 获取统一配置
        self.unified_config = get_config()

        # 服务配置 - 从统一配置加载
        monitor_config = self.unified_config.get_monitor_service_config()
        web_config = self.unified_config.get_web_service_config()

        self.service_configs = {
            'monitor': {
                'name': '监控服务',
                'script': monitor_config['script'],
                'host': monitor_config['host'],
                'port': monitor_config['port'],
                'health_url': monitor_config['health_url'],
                'startup_delay': monitor_config['startup_delay'],
                'max_startup_wait': monitor_config['max_startup_wait']
            },
            'web': {
                'name': 'Web控制台',
                'script': web_config['script'],
                'host': web_config['host'],
                'port': web_config['port'],
                'health_url': web_config['health_url'],
                'startup_delay': web_config['startup_delay'],
                'max_startup_wait': web_config['max_startup_wait'],
                'depends_on': 'monitor'
            }
        }

    def log(self, message, service_name=None, level='INFO'):
        """统一日志输出"""
        log_with_context(self.logger, level, message, service_name=service_name)

    def print_header(self):
        """显示系统信息 - 使用统一日志系统"""
        # {{ AURA-X: Modify - 将print语句替换为统一日志记录. Approval: 寸止(ID:**********). }}
        self.log("\n" + "="*70)
        self.log("🏠 青城住房监控系统 - 一键启动程序 v2.0")
        self.log("="*70)
        self.log("📊 系统架构: 前后端分离")
        self.log("🔧 服务管理: 进程监控 + 健康检查")
        self.log("="*70)

        self.log("系统环境信息:")
        self.log(f"操作系统: {os.name}")
        self.log(f"Python版本: {sys.version.split()[0]}")
        self.log(f"工作目录: {os.getcwd()}")
        self.log(f"项目根目录: {self.project_root}")
        self.log("-"*70)

    def check_dependencies(self):
        """检查依赖和环境"""
        self.log("正在检查系统依赖...")

        # 1. 检查配置完整性
        self.log("正在验证配置完整性...")
        validation_errors = self.unified_config._validate_config()
        if validation_errors:
            self.log("配置验证发现问题:", level='WARN')
            for error in validation_errors:
                self.log(f"  - {error}", level='WARN')
            self.log("配置验证警告不会阻止启动，但建议修复", level='WARN')
        else:
            self.log("配置验证通过 [OK]")

        # 2. 检查Python脚本是否存在
        missing_scripts = []
        for service_id, config in self.service_configs.items():
            script_path = self.project_root / config['script']
            if not script_path.exists():
                missing_scripts.append(config['script'])
                self.log(f"缺少启动脚本: {config['script']}", level='ERROR')

        if missing_scripts:
            self.log(f"发现 {len(missing_scripts)} 个缺失的启动脚本", level='ERROR')
            return False

        # 3. 检查端口是否被占用
        for service_id, config in self.service_configs.items():
            if self.is_port_in_use(config['host'], config['port']):
                self.log(f"{config['name']} 端口 {config['port']} 已被占用", level='WARN')

        # 4. 显示配置摘要
        config_summary = self.unified_config.get_config_summary()
        self.log(f"当前环境: {config_summary['environment']}")
        self.log(f"用户配置文件: {'存在' if config_summary['user_config_exists'] else '不存在'}")
        self.log(f"Web认证: {'已配置' if config_summary['web_auth_configured'] else '未配置'}")

        self.log("依赖检查完成 [OK]")
        return True

    def is_port_in_use(self, host, port):
        """检查端口是否被占用"""
        import socket
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(1)
                result = sock.connect_ex((host, port))
                return result == 0
        except socket.error as e:
            self.log(f"端口检查失败 {host}:{port} - {str(e)}", level='DEBUG')
            return False
        except Exception as e:
            self.log(f"端口检查异常 {host}:{port} - {str(e)}", level='DEBUG')
            return False

    def check_service_health(self, service_id):
        """检查服务健康状态"""
        config = self.service_configs[service_id]
        try:
            timeout = self.unified_config.get('service_management.health_check_timeout', 3)
            response = requests.get(config['health_url'], timeout=timeout)
            return response.status_code == 200
        except requests.RequestException as e:
            self.log(f"健康检查请求失败: {str(e)}", service_id, 'DEBUG')
            return False
        except Exception as e:
            self.log(f"健康检查异常: {str(e)}", service_id, 'DEBUG')
            return False

    def wait_for_service_ready(self, service_id):
        """等待服务启动完成"""
        config = self.service_configs[service_id]
        self.log(f"等待 {config['name']} 启动完成...", service_id)

        # 初始延迟
        time.sleep(config['startup_delay'])

        # 轮询检查服务状态
        start_time = time.time()
        while time.time() - start_time < config['max_startup_wait']:
            if self.check_service_health(service_id):
                self.log(f"{config['name']} 启动成功 [OK]", service_id)
                return True

            if self.shutdown_requested:
                return False

            time.sleep(1)

        self.log(f"{config['name']} 启动超时", service_id, 'ERROR')
        return False

    def start_service(self, service_id):
        """启动单个服务"""
        config = self.service_configs[service_id]

        # 检查依赖服务
        if 'depends_on' in config:
            dependency = config['depends_on']
            if dependency not in self.services or self.services[dependency]['process'].poll() is not None:
                self.log(f"{config['name']} 依赖的服务 {dependency} 未运行", service_id, 'ERROR')
                return False

        self.log(f"正在启动 {config['name']}...", service_id)

        try:
            script_path = self.project_root / config['script']

            # 创建进程
            process = subprocess.Popen(
                [sys.executable, str(script_path)],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )

            # 保存进程信息
            self.services[service_id] = {
                'process': process,
                'config': config,
                'start_time': time.time()
            }

            # 启动日志输出线程
            log_thread = threading.Thread(
                target=self._log_service_output,
                args=(service_id, process),
                daemon=True
            )
            log_thread.start()

            # 等待服务就绪
            if self.wait_for_service_ready(service_id):
                self.log(f"{config['name']} 启动完成，PID: {process.pid}", service_id)
                return True
            else:
                self.stop_service(service_id)
                return False

        except Exception as e:
            self.log(f"启动 {config['name']} 失败: {str(e)}", service_id, 'ERROR')
            return False

    def _log_service_output(self, service_id, process):
        """输出服务日志"""
        try:
            for line in iter(process.stdout.readline, ''):
                if line.strip():
                    self.log(line.strip(), service_id, 'DEBUG')
                if process.poll() is not None:
                    break
        except (BrokenPipeError, ValueError) as e:
            # 进程结束时可能出现这些异常，这是正常的
            self.log(f"服务日志输出结束: {str(e)}", service_id, 'DEBUG')
        except Exception as e:
            self.log(f"服务日志输出异常: {str(e)}", service_id, 'WARN')

    def stop_service(self, service_id):
        """停止单个服务"""
        if service_id not in self.services:
            return True

        config = self.service_configs[service_id]
        service_info = self.services[service_id]
        process = service_info['process']

        self.log(f"正在停止 {config['name']}...", service_id)

        try:
            # 尝试优雅关闭
            process.terminate()

            # 等待进程结束
            try:
                process.wait(timeout=10)
                self.log(f"{config['name']} 已停止", service_id)
            except subprocess.TimeoutExpired:
                self.log(f"强制结束 {config['name']}", service_id, 'WARN')
                process.kill()
                process.wait()

            del self.services[service_id]
            return True

        except Exception as e:
            self.log(f"停止 {config['name']} 失败: {str(e)}", service_id, 'ERROR')
            return False

    def start_all_services(self):
        """按依赖顺序启动所有服务"""
        self.log("开始启动所有服务...")

        # 按依赖顺序排序
        start_order = []
        remaining = list(self.service_configs.keys())

        while remaining:
            for service_id in remaining[:]:
                config = self.service_configs[service_id]
                dependency = config.get('depends_on')

                if not dependency or dependency in start_order:
                    start_order.append(service_id)
                    remaining.remove(service_id)

            if len(remaining) == len([s for s in remaining]):
                # 避免无限循环
                start_order.extend(remaining)
                break

        self.log(f"启动顺序: {' -> '.join([self.service_configs[s]['name'] for s in start_order])}")

        # 依次启动服务
        for service_id in start_order:
            if self.shutdown_requested:
                break

            if not self.start_service(service_id):
                self.log(f"启动服务失败，停止后续启动", level='ERROR')
                return False

        self.log("所有服务启动完成 🎉")
        return True

    def stop_all_services(self):
        """停止所有服务"""
        self.log("开始停止所有服务...")

        # 按启动顺序的反向停止
        service_ids = list(self.services.keys())
        service_ids.reverse()

        for service_id in service_ids:
            self.stop_service(service_id)

        self.log("所有服务已停止")

    def monitor_services(self):
        """监控服务状态"""
        self.log("开始监控服务状态...")

        while not self.shutdown_requested:
            try:
                # 检查每个服务的状态
                for service_id in list(self.services.keys()):
                    service_info = self.services[service_id]
                    process = service_info['process']

                    # 检查进程是否还在运行
                    if process.poll() is not None:
                        config = service_info['config']
                        self.log(f"{config['name']} 进程意外退出，退出码: {process.returncode}", service_id, 'ERROR')
                        del self.services[service_id]

                time.sleep(self.unified_config.get('service_management.check_interval', 5))  # 使用配置的检查间隔

            except Exception as e:
                self.log(f"监控服务时出错: {str(e)}", level='ERROR')
                time.sleep(5)

    def setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            self.log(f"收到信号 {signum}，开始关闭所有服务...")
            self.shutdown_requested = True

        signal.signal(signal.SIGINT, signal_handler)
        if hasattr(signal, 'SIGTERM'):
            signal.signal(signal.SIGTERM, signal_handler)

    def show_status(self):
        """显示服务状态 - 使用统一日志系统"""
        # {{ AURA-X: Modify - 将print语句替换为统一日志记录. Approval: 寸止(ID:**********). }}
        self.log("\n" + "="*50)
        self.log("📊 服务运行状态")
        self.log("="*50)

        for service_id, config in self.service_configs.items():
            if service_id in self.services:
                process = self.services[service_id]['process']
                status = "🟢 运行中" if process.poll() is None else "🔴 已停止"
                uptime = time.time() - self.services[service_id]['start_time']
                self.log(f"{config['name']}: {status} (PID: {process.pid}, 运行时间: {uptime:.1f}s)")
                self.log(f"  地址: http://{config['host']}:{config['port']}")
            else:
                self.log(f"{config['name']}: 🔴 未启动")

        self.log("="*50)
        self.log("💡 按 Ctrl+C 可优雅停止所有服务")
        self.log("="*50 + "\n")

    def run(self):
        """运行服务管理器"""
        try:
            # 激活SSL错误抑制，减少子进程的无害警告
            try:
                from core.exceptions import activate_ssl_suppression
                activate_ssl_suppression()
                self.log("SSL错误抑制已激活", level='DEBUG')
            except ImportError:
                self.log("SSL错误抑制模块不可用，继续启动", level='DEBUG')

            # 设置信号处理器
            self.setup_signal_handlers()

            # 打印头部信息
            self.print_header()

            # 检查依赖
            if not self.check_dependencies():
                self.log("依赖检查失败，退出", level='ERROR')
                return False

            # 启动所有服务
            if not self.start_all_services():
                self.log("服务启动失败，退出", level='ERROR')
                return False

            # 显示状态
            self.show_status()

            # 监控服务
            self.monitor_services()

        except KeyboardInterrupt:
            self.log("收到中断信号，开始关闭...")
        except Exception as e:
            self.log(f"运行时出错: {str(e)}", level='ERROR')
        finally:
            # 停止所有服务
            self.stop_all_services()
            self.log("程序已退出")

        return True


def main():
    """主函数"""
    manager = ServiceManager()
    success = manager.run()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()