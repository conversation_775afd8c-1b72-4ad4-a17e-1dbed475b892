"""
登录助手模块 - 包含呼和浩特住房登录功能和OCR验证码识别
"""

import os
import json
import time
import random
import base64
import io
import datetime
import requests
from datetime import datetime, timedelta
from flask import current_app

# 导入代理管理器
try:
    from core.network.proxy_manager import ProxyManager
    PROXY_MANAGER_AVAILABLE = True
except ImportError:
    PROXY_MANAGER_AVAILABLE = False
    print("警告: ProxyManager 不可用，将使用直连模式")

# 尝试导入图像处理相关库
try:
    from PIL import Image, ImageFilter, ImageEnhance
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False

try:
    import cv2
    CV2_AVAILABLE = True
except ImportError:
    CV2_AVAILABLE = False

try:
    import pytesseract

    # Tesseract 路径检测
    def find_tesseract_cmd():
        """智能检测Tesseract命令路径"""
        import shutil
        import platform

        system = platform.system()

        # 常用路径列表
        common_paths = []
        if system == 'Windows':
            common_paths = [
                r"C:\Program Files\Tesseract-OCR\tesseract.exe",
                r"C:\Program Files (x86)\Tesseract-OCR\tesseract.exe",
                r"C:\Users\<USER>\AppData\Local\Tesseract-OCR\tesseract.exe".format(os.getenv('USERNAME', 'user')),
                r"D:\Program Files\Tesseract-OCR\tesseract.exe",
                r"E:\Program Files\Tesseract-OCR\tesseract.exe"
            ]
        elif system == 'Linux':
            common_paths = [
                "/usr/bin/tesseract",
                "/usr/local/bin/tesseract",
                "/opt/tesseract/bin/tesseract",
                "/snap/bin/tesseract"
            ]
        elif system == 'Darwin':  # macOS
            common_paths = [
                "/usr/local/bin/tesseract",
                "/opt/homebrew/bin/tesseract",
                "/usr/bin/tesseract"
            ]

        # 首先尝试系统PATH中查找
        tesseract_cmd = shutil.which('tesseract')
        if tesseract_cmd:
            return tesseract_cmd

        # 逐个检查常用路径
        for path in common_paths:
            if os.path.isfile(path):
                return path

        return None

    # 设置Tesseract命令路径
    detected_cmd = find_tesseract_cmd()
    if detected_cmd:
        pytesseract.pytesseract.tesseract_cmd = detected_cmd
        #print(f"✅ 检测到Tesseract路径: {detected_cmd}")
    else:
        print(" 未检测到Tesseract，OCR功能可能不可用")
        if not os.getenv('PYTESSERACT_PATH_WARNING_SHOWN'):
            os.environ['PYTESSERACT_PATH_WARNING_SHOWN'] = '1'
            print("📋 请安装Tesseract OCR引擎，或设置环境变量TESSERACT_CMD")

    # pytesseract导入成功
    TESSERACT_AVAILABLE = True

except ImportError:
    print("请安装pytesseract: pip install pytesseract")
    TESSERACT_AVAILABLE = False

# 导入动态请求头生成器和SSL配置管理器
try:
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..'))
    from core.network.dynamic_header_generator import get_global_header_generator
    from core.ssl_manager import get_ssl_manager
    DYNAMIC_HEADERS_AVAILABLE = True
    SSL_MANAGER_AVAILABLE = True
except ImportError:
    DYNAMIC_HEADERS_AVAILABLE = False
    SSL_MANAGER_AVAILABLE = False
    print("警告: 动态请求头生成器或SSL配置管理器不可用，将使用固定配置")


class HuhhothomeLoginHelper:
    """呼和浩特住房登录助手 - 增强版验证码识别，智能代理复用"""

    # 类级别标志位，控制重复日志输出
    _headers_log_printed = False
    _headers_warning_printed = False

    def __init__(self):
        self.session = requests.Session()

        # 使用SSL配置管理器设置SSL验证
        if SSL_MANAGER_AVAILABLE:
            self.ssl_manager = get_ssl_manager()
            # 不在初始化时设置固定的SSL验证，而是在每个请求中动态获取
            # self.session.verify 将在请求方法中动态设置
        else:
            self.session.verify = False  # 回退到禁用SSL验证
            self.ssl_manager = None

        self.base_url = "https://www.huhhothome.cn"
        self.application = "jqHj7ddxI1smOEkmKSD"
        self.domainid = "LAMDyAh4HSdnug1KdKL"

        # 获取动态请求头生成器
        if DYNAMIC_HEADERS_AVAILABLE:
            self.header_generator = get_global_header_generator()
            # 使用动态生成的请求头作为基础
            self.headers = self._get_dynamic_headers()
            # 只在第一次创建实例时输出日志
            if not HuhhothomeLoginHelper._headers_log_printed:
                current_app.logger.info(f"登录助手已启用动态请求头: {self.headers.get('User-Agent', '')[:50]}...")
                HuhhothomeLoginHelper._headers_log_printed = True
        else:
            # 如果动态请求头不可用，使用固定请求头
            self.headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.57 Safari/537.36",
                "Content-Type": "application/json",
                "Accept": "application/json, text/javascript, */*; q=0.01",
                "Sec-Ch-Ua": "\"Not(A:Brand\";v=\"24\", \"Chromium\";v=\"122\"",
                "Sec-Ch-Ua-Mobile": "?0",
                "Sec-Ch-Ua-Platform": "\"Windows\"",
                "Sec-Fetch-Site": "same-origin",
                "Sec-Fetch-Mode": "cors",
                "Sec-Fetch-Dest": "empty",
                "Referer": "https://www.huhhothome.cn/web/index.html",
                "Requesttype": "-"
            }
            # 只在第一次创建实例时输出警告
            if not HuhhothomeLoginHelper._headers_warning_printed:
                current_app.logger.warning("登录助手使用固定请求头（动态生成器不可用）")
                HuhhothomeLoginHelper._headers_warning_printed = True

        # 添加登录助手特有的请求头
        self.headers.update({
            "Content-Type": "application/json",
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "Referer": "https://www.huhhothome.cn/web/index.html",
            "Requesttype": "-"
        })

        # 成功识别的验证码模式历史（用于优化后续识别）
        self.successful_patterns = []

        # 智能登录配置
        self.max_captcha_attempts = 50  # 最大验证码识别尝试次数
        self.captcha_retry_delay = 0.5   # 验证码重试延迟（秒）
        self.success_threshold = 1       # 成功发送短信的次数阈值

        # 代理管理器实例 - 整个登录流程复用
        self.proxy_manager = None

    def _get_dynamic_headers(self):
        """获取动态生成的请求头"""
        if DYNAMIC_HEADERS_AVAILABLE:
            return self.header_generator.get_random_headers()
        else:
            return {}

    def _refresh_headers(self):
        """刷新请求头（20%概率刷新，避免过于频繁）"""
        if DYNAMIC_HEADERS_AVAILABLE and random.random() < 0.2:  # 20%概率刷新
            new_headers = self._get_dynamic_headers()
            # 保留登录助手特有的头信息
            special_headers = {
                "Content-Type": self.headers.get("Content-Type"),
                "Accept": self.headers.get("Accept"),
                "Referer": self.headers.get("Referer"),
                "Requesttype": self.headers.get("Requesttype")
            }

            self.headers = new_headers
            # 重新添加特有头信息
            for key, value in special_headers.items():
                if value:
                    self.headers[key] = value

    def _log_ocr_unavailable_error(self):
        """记录OCR不可用错误并提供详细的安装指导"""
        import platform

        system = platform.system()
        current_app.logger.error("❌ OCR功能不可用")

        if not TESSERACT_AVAILABLE:
            current_app.logger.error("📋 Tesseract OCR引擎不可用")
            if system == 'Linux':
                current_app.logger.error("🔧 Linux系统安装方法:")
                current_app.logger.error("   Ubuntu/Debian: sudo apt-get install -y tesseract-ocr tesseract-ocr-chi-sim")
                current_app.logger.error("   CentOS/RHEL: sudo yum install -y epel-release && sudo yum install -y tesseract tesseract-langpack-chi_sim")
            elif system == 'Windows':
                current_app.logger.error("🔧 Windows系统安装方法:")
                current_app.logger.error("   下载并安装: https://github.com/UB-Mannheim/tesseract/wiki")
            elif system == 'Darwin':
                current_app.logger.error("🔧 macOS系统安装方法:")
                current_app.logger.error("   brew install tesseract")

        if not PIL_AVAILABLE:
            current_app.logger.error("📋 PIL (Pillow) 库不可用")
            current_app.logger.error("🔧 安装方法: pip install Pillow>=9.0.0")

        current_app.logger.error("📚 详细安装指南请参考: LINUX_OCR_INSTALL.md")
        current_app.logger.error("🆘 如需帮助，请确保按照文档完成系统级和Python级的依赖安装")

    def initialize_proxy_once(self):
        """一次性初始化代理管理器，整个登录流程复用"""
        if self.proxy_manager is not None:
            current_app.logger.debug("🔄 代理管理器已初始化，复用现有实例")
            return True

        try:
            # 第一优先级：使用monitor中的proxy_manager
            if hasattr(current_app, 'monitor') and current_app.monitor:
                if hasattr(current_app.monitor, 'proxy_manager') and current_app.monitor.proxy_manager:
                    current_app.logger.info("✅ 复用房源监控的代理管理器")
                    self.proxy_manager = current_app.monitor.proxy_manager

                    # 如果需要，刷新代理IP
                    if not self.proxy_manager.current_proxy or self.proxy_manager.is_proxy_expired():
                        current_app.logger.info("🔄 刷新代理IP...")
                        self.proxy_manager.update_proxy_sync()

                    if self.proxy_manager.current_proxy:
                        current_app.logger.info(f"🌐 登录将使用代理: {self.proxy_manager.current_proxy}")
                        return True
                    else:
                        current_app.logger.warning(" 代理管理器无可用代理IP")

            # 第二优先级：从配置创建临时实例
            if PROXY_MANAGER_AVAILABLE:
                proxy_api_url = None
                try:
                    # 尝试多种配置获取方式
                    proxy_api_url = (
                        current_app.config.get('PROXY_API_URL') or
                        getattr(current_app.config_manager, 'proxy_api_url', None)
                    )
                    if hasattr(current_app.config_manager, 'get'):
                        proxy_api_url = proxy_api_url or current_app.config_manager.get('proxy_api_url', None)
                except Exception as e:
                    current_app.logger.debug(f"获取代理配置时出错: {e}")

                if proxy_api_url:
                    current_app.logger.info("🔧 创建临时代理管理器实例...")
                    self.proxy_manager = ProxyManager(proxy_api_url)
                    # 获取首个代理IP
                    success = self.proxy_manager.update_proxy_sync()
                    if success and self.proxy_manager.current_proxy:
                        current_app.logger.info(f"🌐 登录将使用代理: {self.proxy_manager.current_proxy}")
                        return True
                    else:
                        current_app.logger.warning(" 临时代理管理器获取代理失败")
                        self.proxy_manager = None
                else:
                    current_app.logger.info(" 未配置代理API URL")

            current_app.logger.info("🚫 代理功能不可用，将使用直连模式")
            return False

        except Exception as e:
            current_app.logger.error(f"代理初始化失败: {e}")
            self.proxy_manager = None
            return False

    def get_smart_proxy_manager(self):
        """智能获取代理管理器 - 优先使用monitor中的实例，否则创建临时实例"""
        try:
            # 第一优先级：使用monitor中的proxy_manager
            if hasattr(current_app, 'monitor') and current_app.monitor:
                if hasattr(current_app.monitor, 'proxy_manager') and current_app.monitor.proxy_manager:
                    current_app.logger.debug("✅ 复用房源监控的代理管理器")
                    return current_app.monitor.proxy_manager

            # 第二优先级：从配置创建临时实例
            if PROXY_MANAGER_AVAILABLE:
                proxy_api_url = None
                try:
                    # 尝试多种配置获取方式
                    proxy_api_url = (
                        current_app.config.get('PROXY_API_URL') or
                        getattr(current_app.config_manager, 'proxy_api_url', None)
                    )
                    if hasattr(current_app.config_manager, 'get'):
                        proxy_api_url = proxy_api_url or current_app.config_manager.get('proxy_api_url', None)
                except Exception as e:
                    current_app.logger.debug(f"获取代理配置时出错: {e}")

                if proxy_api_url:
                    current_app.logger.debug("🔧 创建临时代理管理器实例")
                    temp_proxy_manager = ProxyManager(proxy_api_url)
                    # 初始化临时代理
                    temp_proxy_manager.update_proxy_sync()
                    return temp_proxy_manager
                else:
                    current_app.logger.debug(" 未配置代理API URL")

            current_app.logger.debug("🚫 代理功能不可用，将使用直连模式")
            return None

        except Exception as e:
            current_app.logger.error(f"智能代理获取失败: {e}")
            return None

    def _make_request(self, method: str, url: str, **kwargs):
        """统一的请求方法，使用已初始化的代理管理器和动态请求头"""
        max_retries = 3

        for attempt in range(max_retries):
            try:
                # 刷新请求头
                self._refresh_headers()

                # 确保使用最新的请求头
                if 'headers' not in kwargs:
                    kwargs['headers'] = self.headers.copy()
                else:
                    # 合并用户提供的请求头
                    merged_headers = self.headers.copy()
                    merged_headers.update(kwargs['headers'])
                    kwargs['headers'] = merged_headers

                # 使用已初始化的代理管理器
                if self.proxy_manager:
                    # 只在出错时才检查和刷新代理
                    if attempt > 0:  # 非首次尝试时检查代理状态
                        if self.proxy_manager.is_proxy_expired() or not self.proxy_manager.current_proxy:
                            current_app.logger.debug("代理失效，刷新代理IP...")
                            self.proxy_manager.update_proxy_sync()

                    proxy_dict = self.proxy_manager.get_proxy_dict()
                    if proxy_dict:
                        kwargs['proxies'] = proxy_dict

                # 动态设置SSL验证参数
                if SSL_MANAGER_AVAILABLE and self.ssl_manager and 'verify' not in kwargs:
                    from urllib.parse import urlparse
                    parsed_url = urlparse(url)
                    domain = parsed_url.netloc
                    kwargs['verify'] = self.ssl_manager.get_requests_verify_param('login_helper', domain)

                # 发送请求
                if method.upper() == 'GET':
                    response = self.session.get(url, **kwargs)
                elif method.upper() == 'POST':
                    response = self.session.post(url, **kwargs)
                else:
                    raise ValueError(f"不支持的HTTP方法: {method}")

                response.raise_for_status()
                return response

            except requests.exceptions.RequestException as e:
                exception_type = type(e).__name__
                exception_msg = str(e)
                current_app.logger.warning(f"请求失败 (尝试 {attempt + 1}/{max_retries}): {exception_type} - {exception_msg}")

                # 判断是否为代理相关错误
                is_proxy_error = any(keyword in exception_msg.lower() for keyword in [
                    'proxy', 'connection', 'timeout', 'unreachable', 'refused'
                ])

                # 如果是代理错误且不是最后一次尝试，尝试刷新代理
                if attempt < max_retries - 1 and self.proxy_manager and is_proxy_error:
                    current_app.logger.debug("检测到代理错误，尝试刷新代理IP...")
                    self.proxy_manager.update_proxy_sync()
                    time.sleep(1)  # 等待1秒后重试
                elif attempt < max_retries - 1:
                    # 非代理错误，短暂等待后重试，不刷新代理
                    current_app.logger.debug("非代理错误，等待后重试...")
                    time.sleep(0.5)
                else:
                    # 最后一次尝试失败，抛出异常
                    raise

        return None

    def fetch_captcha_image(self, phone):
        """获取验证码图片"""
        url = f"{self.base_url}/api/dynamicapi/code/code"
        params = {
            "application": self.application,
            "codeid": phone
        }

        try:
            response = self._make_request('GET', url, headers=self.headers, params=params)

            if response:
                data = response.json()
                if data["errcode"] == 0.0:
                    img_base64 = data["data"]
                    if "," in img_base64:
                        img_base64 = img_base64.split(",")[1]
                    return img_base64
                else:
                    current_app.logger.error(f"获取验证码API返回错误: {data.get('errmsg', '未知错误')}")
                    return None
            else:
                return None
        except Exception as e:
            current_app.logger.error(f"获取验证码失败: {e}")
            return None

    def quick_ocr_captcha(self, img_base64):
        """快速简化版验证码识别，优先速度"""
        if not TESSERACT_AVAILABLE or not PIL_AVAILABLE:
            self._log_ocr_unavailable_error()
            return []

        try:
            # 解码图片
            img_data = base64.b64decode(img_base64)
            img = Image.open(io.BytesIO(img_data))
        except Exception as e:
            current_app.logger.error(f"图片解码失败: {e}")
            return []

        candidates = []

        # 只使用最有效的3种预处理方法
        quick_preprocessings = [
            lambda x: x.convert('L'),  # 直接灰度化
            self._preprocess_contrast,  # 增强对比度
            self._preprocess_binary,   # 二值化
        ]

        # 只使用最有效的2种PSM模式
        psm_modes = [8, 7]  # 8: 单词模式, 7: 单行文本

        # 只使用一种最通用的字符集
        char_config = 'tessedit_char_whitelist=0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'

        for preprocess_func in quick_preprocessings:
            try:
                processed_img = preprocess_func(img)

                for psm in psm_modes:
                    try:
                        text = pytesseract.image_to_string(
                            processed_img,
                            config=f'--psm {psm} -c {char_config}'
                        ).strip()

                        # 清理文本
                        text = ''.join(c for c in text if c.isalnum())

                        if text and 4 <= len(text) <= 6:
                            candidates.append(text)

                    except Exception:
                        continue

            except Exception:
                continue

        # 去重并返回前3个候选
        unique_candidates = list(dict.fromkeys(candidates))  # 保持顺序去重
        return unique_candidates[:3]

    def advanced_ocr_captcha(self, img_base64):
        """使用增强的OCR技术识别验证码，提供更多候选"""
        if not TESSERACT_AVAILABLE or not PIL_AVAILABLE:
            self._log_ocr_unavailable_error()
            return []

        try:
            # 解码图片
            img_data = base64.b64decode(img_base64)
            img = Image.open(io.BytesIO(img_data))
        except Exception as e:
            current_app.logger.error(f"图片解码失败: {e}")
            return []

        all_results = []

        # 扩展的预处理方法
        preprocessings = [
            self._preprocess_binary,
            self._preprocess_denoise,
            self._preprocess_contrast,
            self._preprocess_sharpen,
            self._preprocess_brightness_adjust,
            self._preprocess_auto_threshold,
            self._preprocess_combined_filters,
        ]

        # 只有在相应库可用时才添加高级预处理方法
        if CV2_AVAILABLE and NUMPY_AVAILABLE:
            preprocessings.extend([
                self._preprocess_adaptive_threshold,
                self._preprocess_morphology_operations,
            ])

        # 为每种预处理方法生成结果
        for preprocess_func in preprocessings:
            try:
                processed_img = preprocess_func(img)

                # 只使用最有效的3种PSM模式
                psm_modes = [8, 7, 13]  # 减少到3种
                # 只使用2种字符集配置
                char_configs = [
                    'tessedit_char_whitelist=0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ',
                    'tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ',
                ]

                for psm in psm_modes:
                    for char_config in char_configs:
                        try:
                            if isinstance(processed_img, np.ndarray) and NUMPY_AVAILABLE:
                                text = pytesseract.image_to_string(
                                    processed_img,
                                    config=f'--psm {psm} -c {char_config}'
                                ).strip()
                            else:
                                text = pytesseract.image_to_string(
                                    processed_img,
                                    config=f'--psm {psm} -c {char_config}'
                                ).strip()

                            # 清理文本
                            text = ''.join(c for c in text if c.isalnum())

                            if text and 4 <= len(text) <= 6:
                                score = self._score_captcha_result(text)
                                all_results.append((text, score, preprocess_func.__name__, psm))

                        except Exception:
                            continue

            except Exception as e:
                current_app.logger.debug(f"预处理方法 {preprocess_func.__name__} 失败: {e}")
                continue

        # 去除重复并按分数排序
        unique_results = {}
        for text, score, method, psm in all_results:
            if text not in unique_results or unique_results[text][1] < score:
                unique_results[text] = (text, score, method, psm)

        # 按分数排序，返回前5个最佳候选
        sorted_results = sorted(unique_results.values(), key=lambda x: x[1], reverse=True)
        return [result[0] for result in sorted_results[:5]]

    def _score_captcha_result(self, text):
        """为验证码结果评分，返回分数"""
        score = 0

        # 长度得分（4-5字符最佳）
        if len(text) == 4:
            score += 20
        elif len(text) == 5:
            score += 15
        elif len(text) == 6:
            score += 10

        # 数字字母混合得分
        has_digit = any(c.isdigit() for c in text)
        has_letter = any(c.isalpha() for c in text)
        if has_digit and has_letter:
            score += 15
        elif has_digit or has_letter:
            score += 10

        # 避免容易混淆的字符
        confusing_chars = ['0O', '1lI', '5S', '6G', '8B']
        for pair in confusing_chars:
            if any(char in text for char in pair):
                score -= 5

        # 常见验证码模式得分
        if text.isalnum() and not text.isdigit() and not text.isalpha():
            score += 10

        return score

    def analyze_captcha_pattern(self, captcha):
        """分析验证码模式"""
        pattern = {
            'length': len(captcha),
            'has_digit': any(c.isdigit() for c in captcha),
            'has_upper': any(c.isupper() for c in captcha),
            'has_lower': any(c.islower() for c in captcha),
            'first_char_type': 'digit' if captcha[0].isdigit() else ('upper' if captcha[0].isupper() else 'lower')
        }
        return pattern

    def optimize_candidates_by_patterns(self, candidates):
        """根据成功模式优化候选结果"""
        if not candidates:
            return candidates

        # 首先过滤明显无效的候选
        filtered_candidates = []
        for candidate in candidates:
            # 基本验证：长度和字符类型
            if len(candidate) < 3 or len(candidate) > 6:
                current_app.logger.debug(f"过滤无效候选（长度）: {candidate}")
                continue

            # 过滤包含特殊字符的候选
            if not candidate.replace('O', '0').replace('I', '1').replace('l', '1').isalnum():
                current_app.logger.debug(f"过滤无效候选（特殊字符）: {candidate}")
                continue

            # 过滤全数字或全字母（通常验证码是混合的）
            if candidate.isdigit() or candidate.isalpha():
                # 但如果历史成功模式中有类似的，则保留
                if not self.successful_patterns:
                    current_app.logger.debug(f"过滤无效候选（单一类型）: {candidate}")
                    continue

            filtered_candidates.append(candidate)

        if not filtered_candidates:
            # 如果过滤后没有候选，返回原始候选的前3个
            current_app.logger.warning("所有候选都被过滤，返回原始候选的前3个")
            return candidates[:3]

        current_app.logger.debug(f"候选过滤后: {len(candidates)} -> {len(filtered_candidates)}")

        # 如果没有历史成功模式，使用通用评分
        if not self.successful_patterns:
            scored_candidates = []
            for candidate in filtered_candidates:
                score = self._score_captcha_result(candidate)
                # 额外评分规则

                # 偏好4-5位长度
                if len(candidate) in [4, 5]:
                    score += 10

                # 偏好数字字母混合，且数字在前
                if candidate and candidate[0].isdigit():
                    score += 5

                # 避免连续相同字符
                has_consecutive = any(candidate[i] == candidate[i+1] for i in range(len(candidate)-1))
                if has_consecutive:
                    score -= 10

                # 避免容易混淆的组合
                confusing_pairs = ['0O', '1lI', '5S', '6G', '8B', '2Z']
                for pair in confusing_pairs:
                    if any(char in candidate for char in pair):
                        score -= 3

                scored_candidates.append((candidate, score))

            # 按分数排序
            scored_candidates.sort(key=lambda x: x[1], reverse=True)
            result = [candidate for candidate, score in scored_candidates]

            current_app.logger.debug(f"通用评分结果: {[(c, s) for c, s in scored_candidates[:5]]}")
            return result

        # 使用历史成功模式进行评分
        scored_candidates = []
        for candidate in filtered_candidates:
            pattern = self.analyze_captcha_pattern(candidate)
            match_score = 0

            for success_pattern in self.successful_patterns:
                # 长度匹配权重最高
                if pattern['length'] == success_pattern['length']:
                    match_score += 15
                elif abs(pattern['length'] - success_pattern['length']) == 1:
                    match_score += 5  # 长度相近也给一些分

                # 字符类型匹配
                if pattern['has_digit'] == success_pattern['has_digit']:
                    match_score += 8
                if pattern['has_upper'] == success_pattern['has_upper']:
                    match_score += 6
                if pattern['has_lower'] == success_pattern['has_lower']:
                    match_score += 6

                # 首字符类型匹配
                if pattern['first_char_type'] == success_pattern['first_char_type']:
                    match_score += 10

            # 结合通用评分
            general_score = self._score_captcha_result(candidate)
            total_score = match_score * 2 + general_score  # 历史模式权重更高

            scored_candidates.append((candidate, total_score, match_score, general_score))

        # 按总分数排序
        scored_candidates.sort(key=lambda x: x[1], reverse=True)
        result = [candidate for candidate, total_score, match_score, general_score in scored_candidates]

        # 记录详细评分信息
        current_app.logger.debug(f"模式匹配评分结果:")
        for i, (candidate, total_score, match_score, general_score) in enumerate(scored_candidates[:5]):
                         current_app.logger.debug(f"  {i+1}. {candidate}: 总分{total_score} (模式{match_score} + 通用{general_score})")

        return result

    def _preprocess_binary(self, img):
        """二值化处理"""
        if not PIL_AVAILABLE:
            return img
        img_gray = img.convert('L')
        threshold = 140
        return img_gray.point(lambda x: 0 if x < threshold else 255, '1')

    def _preprocess_denoise(self, img):
        """去噪处理"""
        if not PIL_AVAILABLE:
            return img
        img_gray = img.convert('L')
        img_filtered = img_gray.filter(ImageFilter.MedianFilter(3))
        return img_filtered

    def _preprocess_contrast(self, img):
        """增强对比度"""
        if not PIL_AVAILABLE:
            return img
        img_gray = img.convert('L')
        enhancer = ImageEnhance.Contrast(img_gray)
        return enhancer.enhance(2.0)

    def _preprocess_sharpen(self, img):
        """锐化图像"""
        if not PIL_AVAILABLE:
            return img
        img_gray = img.convert('L')
        enhancer = ImageEnhance.Sharpness(img_gray)
        return enhancer.enhance(2.0)

    def _preprocess_brightness_adjust(self, img):
        """亮度调整"""
        if not PIL_AVAILABLE:
            return img
        img_gray = img.convert('L')
        enhancer = ImageEnhance.Brightness(img_gray)
        return enhancer.enhance(1.2)

    def _preprocess_auto_threshold(self, img):
        """自动阈值"""
        if not PIL_AVAILABLE:
            return img
        img_gray = img.convert('L')
        if NUMPY_AVAILABLE:
            img_array = np.array(img_gray)
            # 使用Otsu方法自动确定阈值
            threshold = np.mean(img_array)
            return img_gray.point(lambda x: 0 if x < threshold else 255, '1')
        else:
            # 简单的自动阈值
            return img_gray.point(lambda x: 0 if x < 128 else 255, '1')

    def _preprocess_combined_filters(self, img):
        """组合滤波器"""
        if not PIL_AVAILABLE:
            return img
        img_gray = img.convert('L')
        # 先去噪
        img_filtered = img_gray.filter(ImageFilter.MedianFilter(3))
        # 再锐化
        img_sharp = img_filtered.filter(ImageFilter.SHARPEN)
        # 最后增强对比度
        enhancer = ImageEnhance.Contrast(img_sharp)
        return enhancer.enhance(1.5)

    def _preprocess_adaptive_threshold(self, img):
        """自适应阈值 - 使用OpenCV"""
        if not CV2_AVAILABLE or not NUMPY_AVAILABLE or not PIL_AVAILABLE:
            return img.convert('L') if PIL_AVAILABLE else img

        # 转换为opencv格式
        img_gray = img.convert('L')
        img_np = np.array(img_gray)

        # 应用高斯模糊
        blurred = cv2.GaussianBlur(img_np, (5, 5), 0)

        # 自适应阈值
        thresh = cv2.adaptiveThreshold(blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                       cv2.THRESH_BINARY, 11, 2)
        return thresh

    def _preprocess_morphology_operations(self, img):
        """形态学操作 - 使用OpenCV"""
        if not CV2_AVAILABLE or not NUMPY_AVAILABLE or not PIL_AVAILABLE:
            return img.convert('L') if PIL_AVAILABLE else img

        # 转换为opencv格式
        img_gray = img.convert('L')
        img_np = np.array(img_gray)

        # 二值化
        _, binary = cv2.threshold(img_np, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

        # 定义不同的核
        kernel1 = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
        kernel2 = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))

        # 开运算（先腐蚀后膨胀）去除噪点
        opening = cv2.morphologyEx(binary, cv2.MORPH_OPEN, kernel1)
        # 闭运算（先膨胀后腐蚀）填充空洞
        closing = cv2.morphologyEx(opening, cv2.MORPH_CLOSE, kernel2)

        return closing

    def auto_recognize_captcha(self, phone, max_attempts=10):
        """
        智能验证码识别，结合快速识别和高级识别，返回多个高质量候选
        """
        current_app.logger.info(f"开始智能验证码识别，最多尝试 {max_attempts} 次")

        for attempt in range(max_attempts):
            current_app.logger.info(f"第 {attempt + 1}/{max_attempts} 次尝试获取验证码")

            # 获取验证码图片
            img_base64 = self.fetch_captcha_image(phone)
            if not img_base64:
                current_app.logger.warning(f"第 {attempt + 1} 次获取验证码图片失败")
                time.sleep(0.5)
                continue

            try:
                # 1. 先使用快速识别
                quick_candidates = self.quick_ocr_captcha(img_base64)
                current_app.logger.debug(f"快速识别候选: {quick_candidates}")

                # 2. 如果快速识别结果不足，使用高级识别补充
                advanced_candidates = []
                if len(quick_candidates) < 3:
                    advanced_candidates = self.advanced_ocr_captcha(img_base64)
                    current_app.logger.debug(f"高级识别候选: {advanced_candidates}")

                # 3. 合并候选结果并去重
                all_candidates = quick_candidates + advanced_candidates
                unique_candidates = list(dict.fromkeys(all_candidates))  # 保持顺序去重

                if not unique_candidates:
                    current_app.logger.warning(f"第 {attempt + 1} 次识别无有效结果")
                    time.sleep(0.3)
                    continue

                # 4. 根据历史成功模式优化候选顺序
                optimized_candidates = self.optimize_candidates_by_patterns(unique_candidates)

                # 5. 限制候选数量，避免过多尝试
                final_candidates = optimized_candidates[:5]  # 最多5个候选

                current_app.logger.info(f"第 {attempt + 1} 次识别最终候选: {final_candidates}")

                return final_candidates, img_base64

            except Exception as e:
                current_app.logger.error(f"第 {attempt + 1} 次验证码识别过程出错: {e}")
                time.sleep(0.3)
                continue

        current_app.logger.error(f"尝试 {max_attempts} 次后仍无法获得有效验证码候选")
        return [], None

    def send_sms_code(self, phone, img_code):
        """
        发送短信验证码
        优化版：增加详细的错误类型判断和API调用统计
        """
        url = f"{self.base_url}/api/dynamicapi/code/smscode"
        params = {"application": self.application}
        data = {
            "code": img_code,
            "type": "login",
            "phone": phone
        }

        # 记录API调用详情
        current_app.logger.debug(f"🔴 调用短信API: 手机号={phone}, 图形验证码={img_code}")

        try:
            response = self._make_request('POST', url, headers=self.headers, params=params, json=data)

            if response:
                result = response.json()
                error_code = result.get("errcode", -1)
                error_message = result.get('errmsg', '未知错误')

                current_app.logger.debug(f"🔴 API响应: errcode={error_code}, errmsg={error_message}")

                if error_code == 0.0:
                    # 验证码发送成功，记录成功的模式
                    pattern = self.analyze_captcha_pattern(img_code)
                    self.successful_patterns.append(pattern)
                    # 保持历史记录不超过10个
                    if len(self.successful_patterns) > 10:
                        self.successful_patterns.pop(0)

                    current_app.logger.info(f"✅ 短信发送成功: {error_message}")
                    return True, error_message
                else:
                    # 分析错误类型
                    error_type = self._analyze_error_type(error_message)
                    detailed_message = f"{error_message} (错误类型: {error_type})"

                    if error_type == "captcha_error":
                        current_app.logger.warning(f"❌ 图形验证码错误: {error_message}")
                    elif error_type == "rate_limit":
                        current_app.logger.error(f" 可能触发频率限制: {error_message}")
                    elif error_type == "phone_blocked":
                        current_app.logger.error(f"🚫 手机号可能被封禁: {error_message}")
                    else:
                        current_app.logger.error(f"❓ 未知错误类型: {error_message}")

                    return False, detailed_message
            else:
                current_app.logger.error("🔴 API请求失败，无响应")
                return False, "发送验证码失败：网络请求无响应"

        except Exception as e:
            current_app.logger.error(f"🔴 发送短信验证码异常: {e}")
            return False, f"网络异常: {str(e)}"

    def _analyze_error_type(self, error_message):
        """分析错误消息类型"""
        if not error_message:
            return "unknown"

        error_lower = error_message.lower()

        # 图形验证码错误
        captcha_keywords = ['验证码错误', '验证码不正确', '请输入正确', '图形验证码', '验证码有误', 'captcha']
        if any(keyword in error_lower for keyword in captcha_keywords):
            return "captcha_error"

        # 频率限制
        rate_limit_keywords = ['频繁', '限制', '稍后再试', '操作太快', 'too many', 'rate limit']
        if any(keyword in error_lower for keyword in rate_limit_keywords):
            return "rate_limit"

        # 手机号问题
        phone_keywords = ['手机号', '号码', '封禁', '冻结', 'phone', 'mobile']
        if any(keyword in error_lower for keyword in phone_keywords):
            return "phone_blocked"

        # 系统错误
        system_keywords = ['系统', '服务', '维护', 'system', 'service', 'maintenance']
        if any(keyword in error_lower for keyword in system_keywords):
            return "system_error"

        return "unknown"

    def persistent_captcha_recognition(self, phone, max_attempts=None):
        """持续验证码识别，直到成功发送短信验证码为止"""
        if max_attempts is None:
            max_attempts = self.max_captcha_attempts

        current_app.logger.info(f"开始持续验证码识别，手机号: {phone}，最大尝试次数: {max_attempts}")

        attempt_count = 0
        successful_codes = []
        failed_attempts = []

        # 统计信息
        total_fetch_attempts = 0
        total_recognition_attempts = 0
        total_sms_attempts = 0

        # API调用保护 - 记录调用时间
        api_call_times = []
        min_call_interval = 1.0  # 最小调用间隔（秒）

        start_time = time.time()

        while attempt_count < max_attempts:
            attempt_count += 1
            current_app.logger.info(f"━━━ 第 {attempt_count} 轮尝试 ━━━")

            try:
                # 步骤1: 获取验证码图片
                current_app.logger.debug(f"获取验证码图片...")
                total_fetch_attempts += 1

                img_base64 = self.fetch_captcha_image(phone)
                if not img_base64:
                    current_app.logger.warning(f"第 {attempt_count} 轮: 获取验证码图片失败")
                    failed_attempts.append({
                        'attempt': attempt_count,
                        'stage': 'fetch_image',
                        'error': '获取验证码图片失败'
                    })
                    # 动态延迟：失败次数越多，等待时间越长
                    delay = min(2.0 + (attempt_count - 1) * 0.5, 5.0)
                    time.sleep(delay)
                    continue

                # 步骤2: 识别验证码
                current_app.logger.debug(f"识别验证码...")
                total_recognition_attempts += 1

                # 使用现有的快速识别方法
                captcha_candidates = self.quick_ocr_captcha(img_base64)

                if not captcha_candidates:
                    # 如果快速识别失败，尝试高级识别
                    current_app.logger.debug(f"快速识别失败，尝试高级识别...")
                    captcha_candidates = self.advanced_ocr_captcha(img_base64)

                if not captcha_candidates:
                    current_app.logger.warning(f"第 {attempt_count} 轮: 验证码识别失败，无候选结果")
                    failed_attempts.append({
                        'attempt': attempt_count,
                        'stage': 'recognition',
                        'error': '验证码识别失败'
                    })
                    # 识别失败延迟
                    time.sleep(1.0)
                    continue

                # 根据历史成功模式优化候选顺序
                optimized_candidates = self.optimize_candidates_by_patterns(captcha_candidates)

                # 严格限制候选数量，减少API调用
                max_candidates_per_round = 2
                limited_candidates = optimized_candidates[:max_candidates_per_round]

                current_app.logger.info(f"第 {attempt_count} 轮识别到 {len(optimized_candidates)} 个候选，将尝试前 {len(limited_candidates)} 个: {limited_candidates}")

                # 步骤3: 尝试每个候选验证码（限制数量）
                sms_success = False
                successful_code = None

                for candidate_idx, img_code in enumerate(limited_candidates):
                    current_app.logger.debug(f"  准备尝试候选 {candidate_idx + 1}/{len(limited_candidates)}: {img_code}")

                    # API调用保护：确保调用间隔
                    current_time = time.time()
                    if api_call_times:
                        time_since_last_call = current_time - api_call_times[-1]
                        if time_since_last_call < min_call_interval:
                            wait_time = min_call_interval - time_since_last_call
                            current_app.logger.debug(f"  API调用保护：等待 {wait_time:.1f}秒...")
                            time.sleep(wait_time)

                    # 记录API调用时间
                    api_call_times.append(time.time())
                    # 只保留最近20次调用记录
                    if len(api_call_times) > 20:
                        api_call_times.pop(0)

                    total_sms_attempts += 1
                    current_app.logger.info(f"  🔴 尝试候选 {candidate_idx + 1}/{len(limited_candidates)}: {img_code} (第{total_sms_attempts}次API调用)")

                    # 发送短信验证码
                    success, message = self.send_sms_code(phone, img_code)

                    if success:
                        current_app.logger.info(f"🎉 第 {attempt_count} 轮，候选 {candidate_idx + 1} 成功！验证码: {img_code}")
                        successful_code = img_code
                        sms_success = True
                        successful_codes.append({
                            'attempt': attempt_count,
                            'candidate_index': candidate_idx + 1,
                            'code': img_code,
                            'message': message
                        })
                        break
                    else:
                        current_app.logger.warning(f"  ❌ 候选 {candidate_idx + 1} 失败: {message}")
                        failed_attempts.append({
                            'attempt': attempt_count,
                            'stage': 'sms_send',
                            'candidate_index': candidate_idx + 1,
                            'code': img_code,
                            'error': message
                        })

                        # 候选间延迟增加：避免过于频繁的API调用
                        if candidate_idx < len(limited_candidates) - 1:
                            candidate_delay = 0.8  # 增加候选间延迟
                            current_app.logger.debug(f"  候选间延迟: {candidate_delay}秒")
                            time.sleep(candidate_delay)

                # 如果这轮成功了，返回结果
                if sms_success:
                    elapsed_time = time.time() - start_time

                    result = {
                        'success': True,
                        'code': successful_code,
                        'image': f"data:image/jpeg;base64,{img_base64}",
                        'attempt_count': attempt_count,
                        'elapsed_time': elapsed_time,
                        'stats': {
                            'total_fetch_attempts': total_fetch_attempts,
                            'total_recognition_attempts': total_recognition_attempts,
                            'total_sms_attempts': total_sms_attempts,
                            'successful_codes': successful_codes,
                            'failed_attempts_count': len(failed_attempts)
                        },
                        'message': f'验证码识别成功！尝试 {attempt_count} 轮，耗时 {elapsed_time:.1f}秒'
                    }

                    current_app.logger.info(f"✅ 持续验证码识别成功！")
                    return result

                # 这轮失败，继续下一轮
                current_app.logger.warning(f"第 {attempt_count} 轮所有候选都失败，继续下一轮...")

                # 轮次间延迟优化：实现指数退避策略
                round_delay = min(1.5 + (attempt_count - 1) * 0.3, 4.0)  # 1.5秒到4秒之间
                current_app.logger.debug(f"轮次间延迟: {round_delay}秒")
                time.sleep(round_delay)

            except Exception as e:
                current_app.logger.error(f"第 {attempt_count} 轮出现异常: {str(e)}")
                failed_attempts.append({
                    'attempt': attempt_count,
                    'stage': 'exception',
                    'error': str(e)
                })
                # 异常情况延迟
                time.sleep(2.0)
                continue

        # 所有尝试都失败了
        elapsed_time = time.time() - start_time

        result = {
            'success': False,
            'code': None,
            'image': None,
            'attempt_count': attempt_count,
            'elapsed_time': elapsed_time,
            'stats': {
                'total_fetch_attempts': total_fetch_attempts,
                'total_recognition_attempts': total_recognition_attempts,
                'total_sms_attempts': total_sms_attempts,
                'successful_codes': successful_codes,
                'failed_attempts_count': len(failed_attempts),
                'failed_attempts': failed_attempts[-10:]  # 只保留最后10个失败记录
            },
            'message': f'验证码识别失败！尝试 {attempt_count} 轮后仍未成功，总计API调用{total_sms_attempts}次'
        }

        current_app.logger.error(f"❌ 持续验证码识别失败！")
        return result

    def login(self, phone, sms_code, img_code):
        """使用短信验证码登录"""
        url = f"{self.base_url}/api/dynamicapi/usernot/loginphone"
        params = {
            "application": self.application,
            "domainid": self.domainid
        }
        data = {
            "phone": phone,
            "code": sms_code,
            "imgCode": img_code,
            "platform": "PC"
        }

        try:
            response = self._make_request('POST', url, headers=self.headers, params=params, json=data)

            if response:
                result = response.json()
                if result["errcode"] == 0.0:
                    data_field = result.get("data", {})
                    access_token = data_field.get("accesstoken", "")
                    sessioncode = data_field.get("sessioncode", "")

                    # 记录sessioncode获取情况
                    if sessioncode:
                        current_app.logger.info(f"✅ 成功获取sessioncode: {sessioncode[:10]}...{sessioncode[-10:] if len(sessioncode) > 20 else sessioncode}")
                    else:
                        current_app.logger.warning(f" 登录响应中未包含sessioncode字段")
                        current_app.logger.debug(f"登录响应data字段: {data_field}")

                    # 更新headers
                    self.headers["Membertoken"] = access_token
                    self.session.cookies.set("memberToken", access_token)

                    # 获取cookie信息
                    cookies = {}
                    for cookie in self.session.cookies:
                        cookies[cookie.name] = cookie.value

                    return True, access_token, sessioncode, cookies, result.get('errmsg', '登录成功')
                else:
                    current_app.logger.error(f"登录API返回错误: {result.get('errmsg', '未知错误')}")
                    return False, None, None, None, result.get('errmsg', '未知错误')
            else:
                return False, None, None, None, "登录失败"
        except Exception as e:
            current_app.logger.error(f"登录失败: {e}")
            return False, None, None, None, str(e)

    def parse_jwt_token(self, token):
        """解析JWT token获取完整payload"""
        try:
            parts = token.split('.')
            if len(parts) != 3:
                return None

            # 解码payload
            payload_b64 = parts[1]
            # 添加填充
            missing_padding = len(payload_b64) % 4
            if missing_padding:
                payload_b64 += '=' * (4 - missing_padding)

            payload_bytes = base64.urlsafe_b64decode(payload_b64)
            payload = json.loads(payload_bytes.decode('utf-8'))

            return payload
        except Exception as e:
            current_app.logger.error(f"解析JWT token失败: {e}")
            return None

    def get_cookie_remaining_hours(self, token):
        """获取Cookie剩余小时数"""
        try:
            jwt_payload = self.parse_jwt_token(token)
            if jwt_payload and 'exp' in jwt_payload:
                exp_time = datetime.fromtimestamp(jwt_payload['exp'])
                now = datetime.now()
                if exp_time > now:
                    remaining_time = exp_time - now
                    return remaining_time.total_seconds() / 3600
                else:
                    return 0  # 已过期
            return None
        except Exception as e:
            current_app.logger.error(f"计算Cookie剩余时间失败: {e}")
            return None

    def auto_extend_cookie(self, phone, access_token, sessioncode):
        """使用autologin接口自动延长Cookie有效期"""
        current_app.logger.info(f"开始通过autologin接口延长Cookie，手机号: {phone}")

        url = f"{self.base_url}/api/dynamicapi/usernot/autologin"
        params = {
            "application": self.application,
            "domainid": self.domainid
        }
        data = {
            "phone": phone,
            "sessioncode": sessioncode
        }

        # 设置请求头，包含当前的access_token
        extend_headers = self.headers.copy()
        extend_headers["memberToken"] = access_token

        # 日志记录（部分信息脱敏）
        sessioncode_display = sessioncode[:10] + "****" + sessioncode[-10:] if len(sessioncode) > 20 else "****"
        current_app.logger.debug(f"调用autologin接口: 手机号={phone}, sessioncode={sessioncode_display}")

        try:
            response = self._make_request('POST', url, headers=extend_headers, params=params, json=data)

            if response:
                result = response.json()
                error_code = result.get("errcode", -1)
                error_message = result.get('errmsg', '未知错误')

                current_app.logger.debug(f"autologin API响应: errcode={error_code}, errmsg={error_message}")

                if error_code == 0.0:
                    # 延长成功，获取新的access_token
                    data_field = result.get("data", {})
                    new_access_token = data_field.get("accesstoken", "")

                    if new_access_token:
                        # 更新session中的token和cookie
                        self.headers["Membertoken"] = new_access_token
                        self.session.cookies.set("memberToken", new_access_token)

                        # 获取更新后的cookie信息
                        new_cookies = {}
                        for cookie in self.session.cookies:
                            new_cookies[cookie.name] = cookie.value

                        # 计算新的剩余时间
                        remaining_hours = self.get_cookie_remaining_hours(new_access_token)

                        current_app.logger.info(f"✅ autologin延长成功，新的剩余时间: {remaining_hours:.2f} 小时")

                        return {
                            'success': True,
                            'access_token': new_access_token,
                            'cookies': new_cookies,
                            'remaining_hours': remaining_hours,
                            'message': error_message
                        }
                    else:
                        current_app.logger.error(f"❌ autologin响应中缺少新的accesstoken")
                        return {
                            'success': False,
                            'message': 'autologin响应中缺少新的accesstoken'
                        }
                else:
                    current_app.logger.error(f"❌ autologin失败: {error_message}")
                    return {
                        'success': False,
                        'message': f'autologin失败: {error_message}'
                    }
            else:
                current_app.logger.error("❌ autologin API请求失败，无响应")
                return {
                    'success': False,
                    'message': 'autologin API请求失败：网络请求无响应'
                }

        except Exception as e:
            current_app.logger.error(f"❌ autologin API调用异常: {e}")
            return {
                'success': False,
                'message': f'autologin API调用异常: {str(e)}'
            }

    def get_application_records(self, access_token):
        """
        获取申请记录

        Args:
            access_token: 设备的登录token

        Returns:
            dict: 包含申请记录的结果
        """
        try:
            # 初始化代理管理器
            self.initialize_proxy_once()

            url = f"{self.base_url}/api/dynamicapi/apiview/viewdata"

            # 构建查询参数
            params = {
                "application": self.application,
                "apiview": "houseApprove",
                "domainId": self.domainid
            }

            # 构建请求头，包含认证token
            headers = self.headers.copy()
            headers["memberToken"] = access_token
            headers["Cookie"] = f"memberToken={access_token}"

            current_app.logger.info(f"获取申请记录，参数: {params}")

            response = self._make_request('GET', url, headers=headers, params=params)

            if response:
                data = response.json()
                current_app.logger.info(f"申请记录API响应: errcode={data.get('errcode')}, errmsg={data.get('errmsg')}")

                if data.get("errcode") == 0.0:
                    return {
                        'success': True,
                        'data': data.get('data', {}),
                        'message': data.get('errmsg', '获取成功')
                    }
                else:
                    return {
                        'success': False,
                        'error': data.get('errmsg', '获取申请记录失败'),
                        'errcode': data.get('errcode')
                    }
            else:
                return {
                    'success': False,
                    'error': '请求申请记录API失败'
                }

        except Exception as e:
            current_app.logger.error(f"获取申请记录异常: {str(e)}")
            return {
                'success': False,
                'error': f'获取申请记录时发生异常: {str(e)}'
            }

    def get_user_profile(self, access_token):
        """
        获取用户主页详细信息

        Args:
            access_token: 设备的登录token

        Returns:
            dict: 包含用户主页信息的结果
        """
        try:
            # 初始化代理管理器
            self.initialize_proxy_once()

            url = f"{self.base_url}/api/dynamicapi/user/detail"

            # 构建查询参数
            params = {
                "application": self.application
            }

            # 构建请求头，包含认证token
            headers = self.headers.copy()
            headers["memberToken"] = access_token
            headers["Membertoken"] = access_token  # 同时设置两种格式的token头
            headers["Cookie"] = f"memberToken={access_token}"

            current_app.logger.info(f"获取用户主页信息，参数: {params}")

            response = self._make_request('GET', url, headers=headers, params=params)

            if response:
                data = response.json()
                current_app.logger.info(f"用户主页API响应: errcode={data.get('errcode')}, errmsg={data.get('errmsg')}")

                if data.get("errcode") == 0.0:
                    return {
                        'success': True,
                        'data': data.get('data', {}),
                        'message': data.get('errmsg', '获取成功')
                    }
                else:
                    return {
                        'success': False,
                        'error': data.get('errmsg', '获取用户主页信息失败'),
                        'errcode': data.get('errcode')
                    }
            else:
                return {
                    'success': False,
                    'error': '请求用户主页API失败'
                }

        except Exception as e:
            current_app.logger.error(f"获取用户主页信息异常: {str(e)}")
            return {
                'success': False,
                'error': f'获取用户主页信息时发生异常: {str(e)}'
            }