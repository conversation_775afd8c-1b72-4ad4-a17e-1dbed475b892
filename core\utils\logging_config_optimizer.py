#!/usr/bin/env python3
"""
日志配置优化器
提供日志配置的验证、优化和标准化功能
"""

import os
import sys
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List
from datetime import datetime

class LoggingConfigOptimizer:
    """日志配置优化器"""
    
    # 标准日志格式
    STANDARD_FORMAT = "%(asctime)s,%(msecs)03d - %(name)s - %(levelname)s - %(message)s"
    STANDARD_DATE_FORMAT = "%Y-%m-%d %H:%M:%S"
    
    # 推荐的日志级别配置
    ENVIRONMENT_CONFIGS = {
        'development': {
            'level': 'DEBUG',
            'use_colors': True,
            'enable_masking': False,
            'file_log_level': 'DEBUG'
        },
        'testing': {
            'level': 'INFO',
            'use_colors': True,
            'enable_masking': False,
            'file_log_level': 'INFO'
        },
        'production': {
            'level': 'INFO',
            'use_colors': False,
            'enable_masking': True,
            'file_log_level': 'INFO'
        }
    }
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent.parent
        self.issues = []
        self.recommendations = []
    
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """验证日志配置的有效性"""
        self.issues.clear()
        self.recommendations.clear()
        
        # 检查必需的配置项
        required_fields = ['level', 'format', 'date_format']
        for field in required_fields:
            if field not in config:
                self.issues.append(f"缺少必需的配置项: {field}")
        
        # 检查日志级别
        if 'level' in config:
            valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
            if config['level'] not in valid_levels:
                self.issues.append(f"无效的日志级别: {config['level']}")
        
        # 检查格式一致性
        if 'format' in config:
            if config['format'] != self.STANDARD_FORMAT:
                self.recommendations.append(f"建议使用标准格式: {self.STANDARD_FORMAT}")
        
        if 'date_format' in config:
            if config['date_format'] != self.STANDARD_DATE_FORMAT:
                self.recommendations.append(f"建议使用标准日期格式: {self.STANDARD_DATE_FORMAT}")
        
        # 检查文件轮转配置
        if 'file_rotation' in config:
            rotation = config['file_rotation']
            if 'backup_count' in rotation and rotation['backup_count'] < 7:
                self.recommendations.append("建议至少保留7天的日志备份")
        
        return len(self.issues) == 0
    
    def optimize_config(self, config: Dict[str, Any], environment: str = 'production') -> Dict[str, Any]:
        """优化日志配置"""
        optimized = config.copy()
        
        # 应用环境特定配置
        if environment in self.ENVIRONMENT_CONFIGS:
            env_config = self.ENVIRONMENT_CONFIGS[environment]
            optimized.update(env_config)
        
        # 确保使用标准格式
        optimized['format'] = self.STANDARD_FORMAT
        optimized['date_format'] = self.STANDARD_DATE_FORMAT
        
        # 优化文件轮转配置
        if 'file_rotation' not in optimized:
            optimized['file_rotation'] = {}
        
        rotation = optimized['file_rotation']
        rotation.setdefault('when', 'midnight')
        rotation.setdefault('interval', 1)
        rotation.setdefault('backup_count', 30)
        
        # 确保启用文件日志
        optimized.setdefault('enable_file_logging', True)
        
        # 根据环境优化性能设置
        if environment == 'production':
            optimized.setdefault('enable_emoji_conversion', False)  # 生产环境减少处理开销
        else:
            optimized.setdefault('enable_emoji_conversion', True)   # 开发环境增强可读性
        
        return optimized
    
    def generate_config_report(self, config: Dict[str, Any]) -> str:
        """生成配置报告"""
        report = []
        report.append("📊 日志配置分析报告")
        report.append("=" * 50)
        
        # 基本信息
        report.append(f"日志级别: {config.get('level', 'N/A')}")
        report.append(f"使用颜色: {config.get('use_colors', 'N/A')}")
        report.append(f"启用脱敏: {config.get('enable_masking', 'N/A')}")
        report.append(f"文件日志: {config.get('enable_file_logging', 'N/A')}")
        
        # 格式信息
        report.append("\n📝 格式配置:")
        report.append(f"日志格式: {config.get('format', 'N/A')}")
        report.append(f"日期格式: {config.get('date_format', 'N/A')}")
        
        # 文件轮转信息
        if 'file_rotation' in config:
            rotation = config['file_rotation']
            report.append("\n🔄 文件轮转配置:")
            report.append(f"轮转时机: {rotation.get('when', 'N/A')}")
            report.append(f"轮转间隔: {rotation.get('interval', 'N/A')}")
            report.append(f"备份数量: {rotation.get('backup_count', 'N/A')}")
        
        # 问题和建议
        if self.issues:
            report.append("\n❌ 发现的问题:")
            for issue in self.issues:
                report.append(f"  • {issue}")
        
        if self.recommendations:
            report.append("\n💡 优化建议:")
            for rec in self.recommendations:
                report.append(f"  • {rec}")
        
        if not self.issues and not self.recommendations:
            report.append("\n✅ 配置良好，无需优化")
        
        return "\n".join(report)
    
    def check_log_file_permissions(self, log_dir: str) -> List[str]:
        """检查日志文件权限"""
        issues = []
        log_path = Path(log_dir)
        
        if not log_path.exists():
            try:
                log_path.mkdir(parents=True, exist_ok=True)
                issues.append(f"✅ 创建了日志目录: {log_dir}")
            except Exception as e:
                issues.append(f"❌ 无法创建日志目录: {e}")
                return issues
        
        # 检查写入权限
        test_file = log_path / "test_write.tmp"
        try:
            with open(test_file, 'w') as f:
                f.write("test")
            test_file.unlink()
            issues.append("✅ 日志目录写入权限正常")
        except Exception as e:
            issues.append(f"❌ 日志目录写入权限问题: {e}")
        
        return issues
    
    def estimate_log_size(self, config: Dict[str, Any], daily_entries: int = 10000) -> Dict[str, str]:
        """估算日志文件大小"""
        # 估算单条日志的平均大小（字节）
        avg_entry_size = 150  # 包含时间戳、级别、模块名、消息的平均大小
        
        daily_size = daily_entries * avg_entry_size
        backup_count = config.get('file_rotation', {}).get('backup_count', 30)
        total_size = daily_size * backup_count
        
        def format_size(size_bytes):
            for unit in ['B', 'KB', 'MB', 'GB']:
                if size_bytes < 1024:
                    return f"{size_bytes:.1f} {unit}"
                size_bytes /= 1024
            return f"{size_bytes:.1f} TB"
        
        return {
            'daily_size': format_size(daily_size),
            'total_size': format_size(total_size),
            'backup_days': str(backup_count)
        }

def main():
    """主函数 - 用于测试和演示"""
    optimizer = LoggingConfigOptimizer()
    
    # 示例配置
    test_config = {
        'level': 'INFO',
        'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        'date_format': '%Y-%m-%d %H:%M:%S',
        'use_colors': True,
        'enable_masking': True,
        'file_rotation': {
            'when': 'midnight',
            'interval': 1,
            'backup_count': 15
        }
    }
    
    print("🔧 日志配置优化器测试")
    print("=" * 50)
    
    # 验证配置
    is_valid = optimizer.validate_config(test_config)
    print(f"配置有效性: {'✅ 有效' if is_valid else '❌ 无效'}")
    
    # 生成报告
    report = optimizer.generate_config_report(test_config)
    print(f"\n{report}")
    
    # 优化配置
    optimized = optimizer.optimize_config(test_config, 'production')
    print(f"\n🚀 优化后的配置:")
    for key, value in optimized.items():
        print(f"  {key}: {value}")
    
    # 估算大小
    size_info = optimizer.estimate_log_size(optimized)
    print(f"\n📏 日志大小估算:")
    print(f"  每日大小: {size_info['daily_size']}")
    print(f"  总大小: {size_info['total_size']}")
    print(f"  保留天数: {size_info['backup_days']}")

if __name__ == "__main__":
    main()
