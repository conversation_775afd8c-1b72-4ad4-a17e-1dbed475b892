"""
监控服务配置管理 - 独立配置避免循环导入
简化版本 - 使用默认配置，避免循环导入问题
"""

import os
import sys
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# {{ AURA-X: Fix - 避免循环导入，监控服务使用独立配置. }}


class MonitorServiceConfig:
    """监控服务配置类 - 独立配置避免循环导入"""

    def __init__(self):
        # {{ AURA-X: Fix - 使用默认配置，避免循环导入. }}
        # 避免循环导入：monitor_service -> config -> unified_logging -> config
        self._default_config = {
            'monitor_service': {
                'host': '127.0.0.1',
                'port': 8088,
                'debug': False,
                'log_level': 'INFO'
            }
        }

    @property
    def host(self) -> str:
        """获取监控服务主机地址"""
        return self._default_config['monitor_service']['host']

    @property
    def port(self) -> int:
        """获取监控服务端口"""
        return self._default_config['monitor_service']['port']

    @property
    def debug(self) -> bool:
        """获取调试模式设置"""
        return self._default_config['monitor_service']['debug']

    @property
    def database_file(self) -> str:
        """获取数据库文件路径"""
        return 'monitoring.db'

    @property
    def log_level(self) -> str:
        """获取日志级别"""
        return self._default_config['monitor_service']['log_level']

    def get_all_config(self) -> Dict[str, Any]:
        """获取所有配置"""
        return self._default_config

    def reload(self):
        """重新加载配置"""
        # {{ AURA-X: Fix - 独立配置无需重新加载. }}
        pass

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'host': self.host,
            'port': self.port,
            'debug': self.debug,
            'database_file': self.database_file,
            'log_level': self.log_level
        }


# 全局配置实例
monitor_config = MonitorServiceConfig()